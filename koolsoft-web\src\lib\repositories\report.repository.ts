import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import {
  BaseReportFilter,
  AMCReportFilter,
  WarrantyReportFilter,
  ServiceReportFilter,
  SalesReportFilter,
  CustomerReportFilter,
  FinancialReportFilter,
  AnalyticsReportFilter,
} from '@/lib/validations/report.schema';

/**
 * Report Repository
 * Handles all report data operations with proper filtering and pagination
 */
export class ReportRepository {
  /**
   * Get AMC Reports
   */
  async getAMCReports(filters: AMCReportFilter) {
    const whereClause: Prisma.amc_sumWhereInput = {};
    
    // Date filters
    if (filters.startDate || filters.endDate) {
      whereClause.start_date = {};
      if (filters.startDate) whereClause.start_date.gte = filters.startDate;
      if (filters.endDate) whereClause.start_date.lte = filters.endDate;
    }
    
    // Customer filter
    if (filters.customerId) {
      whereClause.cust_id = filters.customerId;
    }
    
    // Executive filter
    if (filters.executiveId) {
      whereClause.exec_id = filters.executiveId;
    }
    
    // Amount filters
    if (filters.amountMin || filters.amountMax) {
      whereClause.amount = {};
      if (filters.amountMin) whereClause.amount.gte = filters.amountMin;
      if (filters.amountMax) whereClause.amount.lte = filters.amountMax;
    }
    
    // Search filter
    if (filters.search) {
      whereClause.OR = [
        { customers: { name: { contains: filters.search, mode: 'insensitive' } } },
        { users: { name: { contains: filters.search, mode: 'insensitive' } } },
      ];
    }
    
    const [data, total] = await Promise.all([
      prisma.amc_sum.findMany({
        where: whereClause,
        include: {
          customers: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          users: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || 'start_date']: filters.sortOrder,
        },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit,
      }),
      prisma.amc_sum.count({ where: whereClause }),
    ]);
    
    return {
      data,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      },
    };
  }
  
  /**
   * Get Warranty Reports
   */
  async getWarrantyReports(filters: WarrantyReportFilter) {
    const whereClause: Prisma.inwarranty_sumWhereInput = {};
    
    // Date filters
    if (filters.startDate || filters.endDate) {
      whereClause.start_date = {};
      if (filters.startDate) whereClause.start_date.gte = filters.startDate;
      if (filters.endDate) whereClause.start_date.lte = filters.endDate;
    }
    
    // Customer filter
    if (filters.customerId) {
      whereClause.cust_id = filters.customerId;
    }
    
    // Executive filter
    if (filters.executiveId) {
      whereClause.exec_id = filters.executiveId;
    }
    
    // Search filter
    if (filters.search) {
      whereClause.OR = [
        { customers: { name: { contains: filters.search, mode: 'insensitive' } } },
        { users: { name: { contains: filters.search, mode: 'insensitive' } } },
      ];
    }
    
    const [data, total] = await Promise.all([
      prisma.inwarranty_sum.findMany({
        where: whereClause,
        include: {
          customers: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          users: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || 'start_date']: filters.sortOrder,
        },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit,
      }),
      prisma.inwarranty_sum.count({ where: whereClause }),
    ]);
    
    return {
      data,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      },
    };
  }
  
  /**
   * Get Service Reports
   */
  async getServiceReports(filters: ServiceReportFilter) {
    const whereClause: Prisma.servicerpt_sumWhereInput = {};
    
    // Date filters
    if (filters.startDate || filters.endDate) {
      whereClause.sr_date = {};
      if (filters.startDate) whereClause.sr_date.gte = filters.startDate;
      if (filters.endDate) whereClause.sr_date.lte = filters.endDate;
    }
    
    // Search filter
    if (filters.search) {
      whereClause.OR = [
        { customers: { name: { contains: filters.search, mode: 'insensitive' } } },
        { users: { name: { contains: filters.search, mode: 'insensitive' } } },
      ];
    }
    
    const [data, total] = await Promise.all([
      prisma.servicerpt_sum.findMany({
        where: whereClause,
        include: {
          customers: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          users: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || 'sr_date']: filters.sortOrder,
        },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit,
      }),
      prisma.servicerpt_sum.count({ where: whereClause }),
    ]);
    
    return {
      data,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      },
    };
  }
  
  /**
   * Get Sales Reports
   */
  async getSalesReports(filters: SalesReportFilter) {
    const whereClause: Prisma.sales_ordersWhereInput = {};
    
    // Date filters
    if (filters.startDate || filters.endDate) {
      whereClause.order_date = {};
      if (filters.startDate) whereClause.order_date.gte = filters.startDate;
      if (filters.endDate) whereClause.order_date.lte = filters.endDate;
    }
    
    // Customer filter
    if (filters.customerId) {
      whereClause.customer_id = filters.customerId;
    }
    
    // Executive filter
    if (filters.executiveId) {
      whereClause.executive_id = filters.executiveId;
    }
    
    // Amount filters
    if (filters.amountMin || filters.amountMax) {
      whereClause.amount = {};
      if (filters.amountMin) whereClause.amount.gte = filters.amountMin;
      if (filters.amountMax) whereClause.amount.lte = filters.amountMax;
    }
    
    // Search filter
    if (filters.search) {
      whereClause.OR = [
        { customers: { name: { contains: filters.search, mode: 'insensitive' } } },
        { users: { name: { contains: filters.search, mode: 'insensitive' } } },
      ];
    }
    
    const [data, total] = await Promise.all([
      prisma.sales_orders.findMany({
        where: whereClause,
        include: {
          customers: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          users: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || 'order_date']: filters.sortOrder,
        },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit,
      }),
      prisma.sales_orders.count({ where: whereClause }),
    ]);
    
    return {
      data,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      },
    };
  }
  
  /**
   * Get Customer Reports
   */
  async getCustomerReports(filters: CustomerReportFilter) {
    const whereClause: Prisma.customersWhereInput = {};

    // Active filter
    if (filters.isActive !== undefined) {
      whereClause.is_active = filters.isActive;
    }

    // City filter
    if (filters.city) {
      whereClause.city = { contains: filters.city, mode: 'insensitive' };
    }

    // State filter
    if (filters.state) {
      whereClause.state = { contains: filters.state, mode: 'insensitive' };
    }

    // Search filter
    if (filters.search) {
      whereClause.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } },
        { phone: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    const [data, total] = await Promise.all([
      prisma.customers.findMany({
        where: whereClause,
        orderBy: {
          [filters.sortBy || 'name']: filters.sortOrder,
        },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit,
      }),
      prisma.customers.count({ where: whereClause }),
    ]);

    return {
      data,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      },
    };
  }

  /**
   * Get Report Statistics
   */
  async getReportStatistics(reportType: string, period: string, startDate?: Date, endDate?: Date) {
    const dateFilter = this.buildDateFilter(period, startDate, endDate);

    switch (reportType.toUpperCase()) {
      case 'AMC':
        return this.getAMCStatistics(dateFilter);
      case 'WARRANTY':
        return this.getWarrantyStatistics(dateFilter);
      case 'SERVICE':
        return this.getServiceStatistics(dateFilter);
      case 'SALES':
        return this.getSalesStatistics(dateFilter);
      case 'CUSTOMER':
        return this.getCustomerStatistics(dateFilter);
      default:
        throw new Error(`Unsupported report type: ${reportType}`);
    }
  }

  /**
   * Build date filter based on period
   */
  private buildDateFilter(period: string, startDate?: Date, endDate?: Date) {
    if (period === 'CUSTOM' && startDate && endDate) {
      return { gte: startDate, lte: endDate };
    }

    const now = new Date();
    const filter: { gte: Date; lte?: Date } = { gte: now };

    switch (period) {
      case 'TODAY':
        filter.gte = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'WEEK':
        filter.gte = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'MONTH':
        filter.gte = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'QUARTER':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3;
        filter.gte = new Date(now.getFullYear(), quarterStart, 1);
        break;
      case 'YEAR':
        filter.gte = new Date(now.getFullYear(), 0, 1);
        break;
    }

    return filter;
  }

  /**
   * Get AMC Statistics
   */
  private async getAMCStatistics(dateFilter: any) {
    const [total, active, expired, revenue] = await Promise.all([
      prisma.amc_sum.count(),
      prisma.amc_sum.count({
        where: {
          end_date: { gte: new Date() },
        },
      }),
      prisma.amc_sum.count({
        where: {
          end_date: { lt: new Date() },
        },
      }),
      prisma.amc_sum.aggregate({
        _sum: { amount: true },
        where: {
          start_date: dateFilter,
        },
      }),
    ]);

    return {
      total,
      active,
      expired,
      revenue: revenue._sum.amount || 0,
      period: dateFilter,
    };
  }

  /**
   * Get Warranty Statistics
   */
  private async getWarrantyStatistics(dateFilter: any) {
    const [total, active, expired] = await Promise.all([
      prisma.inwarranty_sum.count(),
      prisma.inwarranty_sum.count({
        where: {
          end_date: { gte: new Date() },
        },
      }),
      prisma.inwarranty_sum.count({
        where: {
          end_date: { lt: new Date() },
        },
      }),
    ]);

    return {
      total,
      active,
      expired,
      period: dateFilter,
    };
  }

  /**
   * Get Service Statistics
   */
  private async getServiceStatistics(dateFilter: any) {
    const [total, completed, pending] = await Promise.all([
      prisma.servicerpt_sum.count(),
      prisma.servicerpt_sum.count({
        where: {
          status: 'COMPLETED',
        },
      }),
      prisma.servicerpt_sum.count({
        where: {
          status: { in: ['OPEN', 'IN_PROGRESS', 'PENDING'] },
        },
      }),
    ]);

    return {
      total,
      completed,
      pending,
      period: dateFilter,
    };
  }

  /**
   * Get Sales Statistics
   */
  private async getSalesStatistics(dateFilter: any) {
    const [total, revenue, leads, opportunities] = await Promise.all([
      prisma.sales_orders.count(),
      prisma.sales_orders.aggregate({
        _sum: { amount: true },
        where: {
          order_date: dateFilter,
        },
      }),
      prisma.sales_leads.count({
        where: {
          created_at: dateFilter,
        },
      }),
      prisma.sales_opportunities.count({
        where: {
          created_at: dateFilter,
        },
      }),
    ]);

    return {
      total,
      revenue: revenue._sum.amount || 0,
      leads,
      opportunities,
      period: dateFilter,
    };
  }

  /**
   * Get Customer Statistics
   */
  private async getCustomerStatistics(dateFilter: any) {
    const [total, active, inactive] = await Promise.all([
      prisma.customers.count(),
      prisma.customers.count({
        where: { is_active: true },
      }),
      prisma.customers.count({
        where: { is_active: false },
      }),
    ]);

    return {
      total,
      active,
      inactive,
      period: dateFilter,
    };
  }
}
