import { PrismaClient, Prisma } from '@prisma/client';
import { PrismaRepository } from './prisma.repository';

/**
 * Sales Notification Event Repository
 *
 * This repository handles database operations for the Sales Notification Event entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class SalesNotificationEventRepository extends PrismaRepository<
  Prisma.SalesNotificationEventGetPayload<{}>,
  string,
  Prisma.SalesNotificationEventCreateInput,
  Prisma.SalesNotificationEventUpdateInput
> {
  createTransactionRepository(tx: any): SalesNotificationEventRepository {
    return new SalesNotificationEventRepository(tx);
  }
  constructor(prismaClient?: PrismaClient) {
    super('SalesNotificationEvent');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Create a new sales notification event
   * @param eventData Event data
   * @returns Promise resolving to the created event
   */
  async createEvent(eventData: {
    eventType: string;
    entityType: string;
    entityId: string;
    userId?: string;
    customerId?: string;
    executiveId?: string;
    oldStatus?: string;
    newStatus?: string;
    eventData?: any;
  }): Promise<Prisma.SalesNotificationEventGetPayload<{}>> {
    return this.model.create({
      data: {
        eventType: eventData.eventType,
        entityType: eventData.entityType,
        entityId: eventData.entityId,
        userId: eventData.userId,
        customerId: eventData.customerId,
        executiveId: eventData.executiveId,
        oldStatus: eventData.oldStatus,
        newStatus: eventData.newStatus,
        eventData: eventData.eventData,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
    });
  }

  /**
   * Find unprocessed events
   * @param limit Maximum number of events to return
   * @returns Promise resolving to an array of unprocessed events
   */
  async findUnprocessedEvents(limit: number = 100): Promise<Prisma.SalesNotificationEventGetPayload<{
    include: {
      user: { select: { id: true; name: true; email: true; role: true } };
      customer: { select: { id: true; name: true; email: true; phone: true } };
      executive: { select: { id: true; name: true; email: true; designation: true } };
    };
  }>[]> {
    return this.model.findMany({
      where: {
        processed: false,
      },
      orderBy: {
        createdAt: 'asc',
      },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
    });
  }

  /**
   * Mark events as processed
   * @param eventIds Array of event IDs to mark as processed
   * @returns Promise resolving to the number of updated events
   */
  async markAsProcessed(eventIds: string[]): Promise<number> {
    const result = await this.model.updateMany({
      where: {
        id: {
          in: eventIds,
        },
      },
      data: {
        processed: true,
        processedAt: new Date(),
      },
    });

    return result.count;
  }

  /**
   * Find events by entity
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of events
   */
  async findByEntity(
    entityType: string,
    entityId: string,
    skip?: number,
    take?: number
  ): Promise<Prisma.SalesNotificationEventGetPayload<{
    include: {
      user: { select: { id: true; name: true; email: true; role: true } };
      customer: { select: { id: true; name: true; email: true; phone: true } };
      executive: { select: { id: true; name: true; email: true; designation: true } };
    };
  }>[]> {
    return this.model.findMany({
      where: {
        entityType,
        entityId,
      },
      skip,
      take,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
    });
  }

  /**
   * Find events by user
   * @param userId User ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of events
   */
  async findByUser(
    userId: string,
    skip?: number,
    take?: number
  ): Promise<Prisma.SalesNotificationEventGetPayload<{
    include: {
      customer: { select: { id: true; name: true; email: true; phone: true } };
      executive: { select: { id: true; name: true; email: true; designation: true } };
    };
  }>[]> {
    return this.model.findMany({
      where: {
        userId,
      },
      skip,
      take,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
    });
  }

  /**
   * Get event statistics
   * @param startDate Start date for statistics
   * @param endDate End date for statistics
   * @returns Promise resolving to event statistics
   */
  async getStatistics(startDate?: Date, endDate?: Date): Promise<{
    totalEvents: number;
    processedEvents: number;
    unprocessedEvents: number;
    eventsByType: Record<string, number>;
    eventsByEntityType: Record<string, number>;
  }> {
    const where: any = {};
    
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [totalEvents, processedEvents, allEvents] = await Promise.all([
      this.model.count({ where }),
      this.model.count({ where: { ...where, processed: true } }),
      this.model.findMany({
        where,
        select: {
          eventType: true,
          entityType: true,
          processed: true,
        },
      }),
    ]);

    const unprocessedEvents = totalEvents - processedEvents;

    const eventsByType: Record<string, number> = {};
    const eventsByEntityType: Record<string, number> = {};

    allEvents.forEach(event => {
      eventsByType[event.eventType] = (eventsByType[event.eventType] || 0) + 1;
      eventsByEntityType[event.entityType] = (eventsByEntityType[event.entityType] || 0) + 1;
    });

    return {
      totalEvents,
      processedEvents,
      unprocessedEvents,
      eventsByType,
      eventsByEntityType,
    };
  }

  /**
   * Clean up old processed events
   * @param olderThanDays Delete events older than this many days
   * @returns Promise resolving to the number of deleted events
   */
  async cleanupOldEvents(olderThanDays: number = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.model.deleteMany({
      where: {
        processed: true,
        processedAt: {
          lt: cutoffDate,
        },
      },
    });

    return result.count;
  }
}

/**
 * Get the sales notification event repository instance
 * @returns Sales notification event repository instance
 */
export function getSalesNotificationEventRepository(): SalesNotificationEventRepository {
  return new SalesNotificationEventRepository();
}
