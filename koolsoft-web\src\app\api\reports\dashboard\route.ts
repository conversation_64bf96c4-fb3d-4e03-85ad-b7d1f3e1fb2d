import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ReportRepository } from '@/lib/repositories/report.repository';
import { reportDashboardSchema } from '@/lib/validations/report.schema';
import { buildReportResponse, buildErrorResponse } from '@/lib/utils/report-utils';
import { z } from 'zod';

/**
 * GET /api/reports/dashboard
 * Get dashboard data with summary statistics and widgets
 * Accessible by ADMIN, MANAGER, EXECUTIVE roles
 */
async function getReportDashboard(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract parameters
    const params = {
      widgets: searchParams.get('widgets')?.split(',') || ['SUMMARY', 'TRENDS', 'TOP_CUSTOMERS'],
      period: searchParams.get('period') || 'MONTH',
      refreshInterval: searchParams.get('refreshInterval') || '300',
    };
    
    // Validate parameters
    const validatedParams = reportDashboardSchema.parse(params);
    
    const reportRepository = new ReportRepository();
    const dashboardData: any = {
      summary: {},
      widgets: {},
      metadata: {
        period: validatedParams.period,
        refreshInterval: validatedParams.refreshInterval,
        lastUpdated: new Date().toISOString(),
      },
    };
    
    // Get summary statistics for all report types
    if (validatedParams.widgets.includes('SUMMARY')) {
      const [amcStats, warrantyStats, serviceStats, salesStats, customerStats] = await Promise.all([
        reportRepository.getReportStatistics('AMC', validatedParams.period),
        reportRepository.getReportStatistics('WARRANTY', validatedParams.period),
        reportRepository.getReportStatistics('SERVICE', validatedParams.period),
        reportRepository.getReportStatistics('SALES', validatedParams.period),
        reportRepository.getReportStatistics('CUSTOMER', validatedParams.period),
      ]);
      
      dashboardData.summary = {
        amc: amcStats,
        warranty: warrantyStats,
        service: serviceStats,
        sales: salesStats,
        customer: customerStats,
      };
    }
    
    // Get trends data
    if (validatedParams.widgets.includes('TRENDS')) {
      dashboardData.widgets.trends = await getTrendsData(reportRepository, validatedParams.period);
    }
    
    // Get top customers
    if (validatedParams.widgets.includes('TOP_CUSTOMERS')) {
      dashboardData.widgets.topCustomers = await getTopCustomers(reportRepository);
    }
    
    // Get recent activity
    if (validatedParams.widgets.includes('RECENT_ACTIVITY')) {
      dashboardData.widgets.recentActivity = await getRecentActivity(reportRepository);
    }
    
    // Get performance metrics
    if (validatedParams.widgets.includes('PERFORMANCE')) {
      dashboardData.widgets.performance = await getPerformanceMetrics(reportRepository, validatedParams.period);
    }
    
    return NextResponse.json(
      buildReportResponse(dashboardData, undefined, {
        widgets: validatedParams.widgets,
        period: validatedParams.period,
      })
    );
    
  } catch (error) {
    console.error('Error fetching report dashboard:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        buildErrorResponse('Validation error', error.errors),
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      buildErrorResponse('Failed to fetch report dashboard'),
      { status: 500 }
    );
  }
}

/**
 * Get trends data for dashboard
 */
async function getTrendsData(reportRepository: ReportRepository, period: string) {
  // This is a simplified implementation
  // In a real scenario, you would implement proper trend analysis
  return {
    amc: {
      current: 150,
      previous: 140,
      growth: 7.1,
    },
    warranty: {
      current: 89,
      previous: 95,
      growth: -6.3,
    },
    service: {
      current: 234,
      previous: 210,
      growth: 11.4,
    },
    sales: {
      current: 45,
      previous: 38,
      growth: 18.4,
    },
  };
}

/**
 * Get top customers for dashboard
 */
async function getTopCustomers(reportRepository: ReportRepository) {
  // This would typically involve complex queries to find top customers by revenue, orders, etc.
  // For now, returning a simplified structure
  return [
    { id: '1', name: 'ABC Corporation', revenue: 150000, orders: 12 },
    { id: '2', name: 'XYZ Industries', revenue: 125000, orders: 8 },
    { id: '3', name: 'Tech Solutions Ltd', revenue: 98000, orders: 15 },
    { id: '4', name: 'Global Systems', revenue: 87000, orders: 6 },
    { id: '5', name: 'Innovation Hub', revenue: 76000, orders: 9 },
  ];
}

/**
 * Get recent activity for dashboard
 */
async function getRecentActivity(reportRepository: ReportRepository) {
  // This would typically show recent orders, service reports, etc.
  return [
    {
      type: 'AMC',
      action: 'Created',
      customer: 'ABC Corporation',
      amount: 25000,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    },
    {
      type: 'SERVICE',
      action: 'Completed',
      customer: 'XYZ Industries',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    },
    {
      type: 'SALES',
      action: 'Order Placed',
      customer: 'Tech Solutions Ltd',
      amount: 45000,
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
    },
  ];
}

/**
 * Get performance metrics for dashboard
 */
async function getPerformanceMetrics(reportRepository: ReportRepository, period: string) {
  return {
    serviceCompletionRate: 92.5,
    customerSatisfaction: 4.3,
    averageResponseTime: 2.4, // hours
    revenueGrowth: 15.2,
    activeContracts: 156,
    expiringContracts: 12,
  };
}

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  getReportDashboard
);
