import nodemailer from 'nodemailer';
import { getEmailTemplateRepository, getEmailLogRepository } from '@/lib/repositories';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Email configuration
 */
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: string;
}

/**
 * Email data
 */
interface EmailData {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  text?: string;
  html: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

/**
 * Email service
 * 
 * This service handles email operations like sending emails and logging them.
 */
export class EmailService {
  private transporter: nodemailer.Transporter;
  private config: EmailConfig;
  
  /**
   * Create a new email service instance
   * @param config Email configuration
   */
  constructor(config: EmailConfig) {
    this.config = config;
    this.transporter = nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: {
        user: config.auth.user,
        pass: config.auth.pass,
      },
    });
  }
  
  /**
   * Send an email
   * @param data Email data
   * @param customerId Customer ID (optional)
   * @param userId User ID (optional)
   * @returns Promise resolving to the email log
   */
  async sendEmail(data: EmailData, customerId?: string, userId?: string): Promise<any> {
    try {
      // Send email
      const info = await this.transporter.sendMail({
        from: this.config.from,
        to: Array.isArray(data.to) ? data.to.join(',') : data.to,
        cc: data.cc ? (Array.isArray(data.cc) ? data.cc.join(',') : data.cc) : undefined,
        bcc: data.bcc ? (Array.isArray(data.bcc) ? data.bcc.join(',') : data.bcc) : undefined,
        subject: data.subject,
        text: data.text,
        html: data.html,
        attachments: data.attachments,
      });
      
      // Log email
      const emailLogRepository = getEmailLogRepository();
      
      const emailLog = await emailLogRepository.create({
        to: Array.isArray(data.to) ? data.to.join(',') : data.to,
        cc: data.cc ? (Array.isArray(data.cc) ? data.cc.join(',') : data.cc) : undefined,
        bcc: data.bcc ? (Array.isArray(data.bcc) ? data.bcc.join(',') : data.bcc) : undefined,
        subject: data.subject,
        body: data.html,
        status: 'SENT',
        messageId: info.messageId,
        sentAt: new Date(),
        customerId,
        userId,
      });
      
      return emailLog;
    } catch (error) {
      console.error('Error sending email:', error);
      
      // Log email error
      const emailLogRepository = getEmailLogRepository();
      
      const emailLog = await emailLogRepository.create({
        to: Array.isArray(data.to) ? data.to.join(',') : data.to,
        cc: data.cc ? (Array.isArray(data.cc) ? data.cc.join(',') : data.cc) : undefined,
        bcc: data.bcc ? (Array.isArray(data.bcc) ? data.bcc.join(',') : data.bcc) : undefined,
        subject: data.subject,
        body: data.html,
        status: 'ERROR',
        error: error instanceof Error ? error.message : String(error),
        sentAt: new Date(),
        customerId,
        userId,
      });
      
      throw error;
    }
  }
  
  /**
   * Send an email using a template
   * @param templateName Template name
   * @param data Template data
   * @param to Recipient email address(es)
   * @param cc CC email address(es) (optional)
   * @param bcc BCC email address(es) (optional)
   * @param customerId Customer ID (optional)
   * @param userId User ID (optional)
   * @returns Promise resolving to the email log
   */
  async sendTemplateEmail(
    templateName: string,
    data: Record<string, any>,
    to: string | string[],
    cc?: string | string[],
    bcc?: string | string[],
    customerId?: string,
    userId?: string
  ): Promise<any> {
    // Get template
    const emailTemplateRepository = getEmailTemplateRepository();
    const template = await emailTemplateRepository.findByName(templateName);
    
    if (!template) {
      throw new Error(`Email template '${templateName}' not found`);
    }
    
    // Replace placeholders in template
    let html = template.bodyHtml;
    let subject = template.subject;
    
    for (const [key, value] of Object.entries(data)) {
      const placeholder = `{{${key}}}`;
      html = html.replace(new RegExp(placeholder, 'g'), String(value));
      subject = subject.replace(new RegExp(placeholder, 'g'), String(value));
    }
    
    // Send email
    return this.sendEmail(
      {
        to,
        cc,
        bcc,
        subject,
        html,
      },
      customerId,
      userId
    );
  }
}

/**
 * Get the email service instance
 * @returns Email service instance
 */
export function getEmailService(): EmailService {
  // Get email configuration from environment variables
  const config: EmailConfig = {
    host: process.env.EMAIL_HOST || 'smtp.example.com',
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER || '<EMAIL>',
      pass: process.env.EMAIL_PASS || 'password',
    },
    from: process.env.EMAIL_FROM || '<EMAIL>',
  };
  
  return new EmailService(config);
}
