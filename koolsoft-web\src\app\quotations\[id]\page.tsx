'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import {
  FileText,
  Edit,
  Download,
  Mail,
  Copy,
  Trash2,
  ArrowLeft,
  Calendar,
  User,
  Building,
  Phone,
  MapPin,
  DollarSign,
} from 'lucide-react';

interface QuotationDetail {
  id: string;
  quotationNumber: string;
  quotationDate: string;
  validUntil?: string;
  status: string;
  subject?: string;
  notes?: string;
  termsConditions?: string;
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  discount?: number;
  discountType?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  customer: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    mobile?: string;
    address?: string;
    city?: string;
    state?: string;
    pinCode?: string;
  };
  executive: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    designation?: string;
  };
  items: Array<{
    id: string;
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    taxRate?: number;
    taxAmount?: number;
    discount?: number;
    discountType?: string;
    specifications?: string;
    notes?: string;
    product?: {
      id: string;
      name: string;
    };
    model?: {
      id: string;
      name: string;
      specs?: string;
    };
    brand?: {
      id: string;
      name: string;
    };
  }>;
}

/**
 * Quotation Detail Page
 *
 * This page displays detailed information about a specific quotation
 * with options to edit, export, email, duplicate, or delete.
 */
export default function QuotationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [quotation, setQuotation] = useState<QuotationDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const quotationId = params.id as string;

  // Fetch quotation details
  const fetchQuotation = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/quotations/${quotationId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch quotation');
      }

      const data = await response.json();
      if (data.success) {
        setQuotation(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch quotation');
      }
    } catch (error) {
      console.error('Error fetching quotation:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch quotation details. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (quotationId) {
      fetchQuotation();
    }
  }, [quotationId]);

  // Handle export PDF
  const handleExportPDF = async () => {
    try {
      setActionLoading('export');
      const response = await fetch(`/api/quotations/${quotationId}/export?format=pdf`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to export quotation');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `quotation-${quotation?.quotationNumber}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Success',
        description: 'Quotation exported successfully.',
      });
    } catch (error) {
      console.error('Error exporting quotation:', error);
      toast({
        title: 'Error',
        description: 'Failed to export quotation. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  // Handle duplicate quotation
  const handleDuplicate = async () => {
    try {
      setActionLoading('duplicate');
      const response = await fetch(`/api/quotations/${quotationId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({}),
      });

      if (!response.ok) {
        throw new Error('Failed to duplicate quotation');
      }

      const data = await response.json();
      if (data.success) {
        toast({
          title: 'Success',
          description: 'Quotation duplicated successfully.',
        });
        router.push(`/quotations/${data.data.id}`);
      } else {
        throw new Error(data.error || 'Failed to duplicate quotation');
      }
    } catch (error) {
      console.error('Error duplicating quotation:', error);
      toast({
        title: 'Error',
        description: 'Failed to duplicate quotation. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  // Handle delete quotation
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this quotation? This action cannot be undone.')) {
      return;
    }

    try {
      setActionLoading('delete');
      const response = await fetch(`/api/quotations/${quotationId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete quotation');
      }

      const data = await response.json();
      if (data.success) {
        toast({
          title: 'Success',
          description: 'Quotation deleted successfully.',
        });
        router.push('/quotations');
      } else {
        throw new Error(data.error || 'Failed to delete quotation');
      }
    } catch (error) {
      console.error('Error deleting quotation:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete quotation. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'secondary';
      case 'SENT':
        return 'default';
      case 'ACCEPTED':
        return 'default';
      case 'REJECTED':
        return 'destructive';
      case 'EXPIRED':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="Loading..." requireAuth allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading quotation details...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!quotation) {
    return (
      <DashboardLayout title="Quotation Not Found" requireAuth allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}>
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Quotation Not Found</h2>
          <p className="text-gray-600 mb-4">The quotation you're looking for doesn't exist or has been deleted.</p>
          <Button asChild>
            <Link href="/quotations">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Quotations
            </Link>
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title={`Quotation ${quotation.quotationNumber}`} requireAuth allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}>
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Quotation {quotation.quotationNumber}</span>
                <Badge variant={getStatusBadgeVariant(quotation.status)} className="ml-2">
                  {quotation.status}
                </Badge>
              </CardTitle>
              <CardDescription className="text-gray-100">
                {quotation.subject || 'Quotation details and line items'}
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button asChild variant="secondary" size="sm">
                <Link href="/quotations">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Link>
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleExportPDF}
                disabled={actionLoading === 'export'}
              >
                <Download className="h-4 w-4 mr-2" />
                {actionLoading === 'export' ? 'Exporting...' : 'Export PDF'}
              </Button>
              <Button asChild variant="secondary" size="sm">
                <Link href={`/quotations/${quotation.id}/email`}>
                  <Mail className="h-4 w-4 mr-2" />
                  Email
                </Link>
              </Button>
              <Button asChild variant="secondary" size="sm">
                <Link href={`/quotations/${quotation.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Link>
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleDuplicate}
                disabled={actionLoading === 'duplicate'}
              >
                <Copy className="h-4 w-4 mr-2" />
                {actionLoading === 'duplicate' ? 'Duplicating...' : 'Duplicate'}
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                disabled={actionLoading === 'delete'}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {actionLoading === 'delete' ? 'Deleting...' : 'Delete'}
              </Button>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Quotation Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Quotation Number</label>
                    <p className="font-medium">{quotation.quotationNumber}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Status</label>
                    <div className="mt-1">
                      <Badge variant={getStatusBadgeVariant(quotation.status)}>
                        {quotation.status}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Quotation Date</label>
                    <p className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                      {new Date(quotation.quotationDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Valid Until</label>
                    <p className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                      {quotation.validUntil ? new Date(quotation.validUntil).toLocaleDateString() : 'N/A'}
                    </p>
                  </div>
                </div>

                {quotation.subject && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Subject</label>
                    <p>{quotation.subject}</p>
                  </div>
                )}

                {quotation.notes && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Notes</label>
                    <p className="text-gray-700 whitespace-pre-wrap">{quotation.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Line Items */}
            <Card>
              <CardHeader>
                <CardTitle>Line Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {quotation.items.map((item, index) => (
                    <div key={item.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium">Item {index + 1}</h4>
                        <span className="font-semibold">₹{item.totalPrice.toLocaleString()}</span>
                      </div>
                      
                      <p className="text-gray-700 mb-2">{item.description}</p>
                      
                      {item.specifications && (
                        <p className="text-sm text-gray-600 mb-2">{item.specifications}</p>
                      )}
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Quantity:</span>
                          <span className="ml-1 font-medium">{item.quantity}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Unit Price:</span>
                          <span className="ml-1 font-medium">₹{item.unitPrice.toLocaleString()}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Tax Rate:</span>
                          <span className="ml-1 font-medium">{item.taxRate || 0}%</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Tax Amount:</span>
                          <span className="ml-1 font-medium">₹{(item.taxAmount || 0).toLocaleString()}</span>
                        </div>
                      </div>

                      {item.product && (
                        <div className="mt-2 text-sm">
                          <span className="text-gray-500">Product:</span>
                          <span className="ml-1">{item.product.name}</span>
                        </div>
                      )}

                      {item.model && (
                        <div className="mt-1 text-sm">
                          <span className="text-gray-500">Model:</span>
                          <span className="ml-1">{item.model.name}</span>
                        </div>
                      )}

                      {item.brand && (
                        <div className="mt-1 text-sm">
                          <span className="text-gray-500">Brand:</span>
                          <span className="ml-1">{item.brand.name}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Terms and Conditions */}
            {quotation.termsConditions && (
              <Card>
                <CardHeader>
                  <CardTitle>Terms & Conditions</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 whitespace-pre-wrap">{quotation.termsConditions}</p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Building className="h-5 w-5" />
                  <span>Customer</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="font-medium">{quotation.customer.name}</p>
                </div>
                
                {quotation.customer.address && (
                  <div className="flex items-start space-x-2">
                    <MapPin className="h-4 w-4 mt-0.5 text-gray-400" />
                    <div>
                      <p className="text-sm">{quotation.customer.address}</p>
                      {(quotation.customer.city || quotation.customer.state || quotation.customer.pinCode) && (
                        <p className="text-sm text-gray-600">
                          {[quotation.customer.city, quotation.customer.state, quotation.customer.pinCode]
                            .filter(Boolean)
                            .join(', ')}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {quotation.customer.phone && (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">{quotation.customer.phone}</span>
                  </div>
                )}

                {quotation.customer.email && (
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">{quotation.customer.email}</span>
                  </div>
                )}

                {quotation.contactPerson && (
                  <Separator />
                )}

                {quotation.contactPerson && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Contact Person</label>
                    <p className="text-sm">{quotation.contactPerson}</p>
                  </div>
                )}

                {quotation.contactPhone && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Contact Phone</label>
                    <p className="text-sm">{quotation.contactPhone}</p>
                  </div>
                )}

                {quotation.contactEmail && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Contact Email</label>
                    <p className="text-sm">{quotation.contactEmail}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Executive Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span>Sales Executive</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="font-medium">{quotation.executive.name}</p>
                  {quotation.executive.designation && (
                    <p className="text-sm text-gray-600">{quotation.executive.designation}</p>
                  )}
                </div>

                {quotation.executive.email && (
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">{quotation.executive.email}</span>
                  </div>
                )}

                {quotation.executive.phone && (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">{quotation.executive.phone}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Totals */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5" />
                  <span>Totals</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="font-medium">₹{quotation.subtotal.toLocaleString()}</span>
                </div>

                {quotation.discount && quotation.discount > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Discount:</span>
                    <span className="font-medium">
                      {quotation.discountType === 'PERCENTAGE' 
                        ? `${quotation.discount}%` 
                        : `₹${quotation.discount.toLocaleString()}`}
                    </span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-gray-600">Tax Amount:</span>
                  <span className="font-medium">₹{quotation.taxAmount.toLocaleString()}</span>
                </div>

                <Separator />

                <div className="flex justify-between text-lg font-bold">
                  <span>Total Amount:</span>
                  <span>₹{quotation.totalAmount.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
