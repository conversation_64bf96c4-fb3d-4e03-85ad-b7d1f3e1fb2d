import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { QuotationRepository } from '@/lib/repositories/quotation.repository';
import { ActivityLogService } from '@/lib/services/activity-log.service';

/**
 * Export Quotations API
 * 
 * Exports quotations data in various formats (CSV, Excel)
 * with filtering and search capabilities.
 */

async function handleExportQuotations(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'csv';
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    const quotationRepo = new QuotationRepository();
    
    // Build filters
    const filters: any = {};
    
    if (search) {
      filters.OR = [
        { quotationNumber: { contains: search, mode: 'insensitive' } },
        { subject: { contains: search, mode: 'insensitive' } },
        { customer: { name: { contains: search, mode: 'insensitive' } } },
        { executive: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }
    
    if (status) {
      filters.status = status;
    }
    
    if (startDate) {
      filters.quotationDate = {
        ...filters.quotationDate,
        gte: new Date(startDate),
      };
    }
    
    if (endDate) {
      filters.quotationDate = {
        ...filters.quotationDate,
        lte: new Date(endDate),
      };
    }

    // Fetch all quotations matching filters
    const quotations = await quotationRepo.findMany({
      where: filters,
      include: {
        customer: {
          select: {
            name: true,
            email: true,
            phone: true,
            city: true,
            state: true,
          },
        },
        executive: {
          select: {
            name: true,
            email: true,
            designation: true,
          },
        },
        items: {
          select: {
            description: true,
            quantity: true,
            unitPrice: true,
            totalPrice: true,
            taxRate: true,
            taxAmount: true,
          },
        },
      },
      orderBy: { quotationDate: 'desc' },
    });

    if (format === 'csv') {
      // Generate CSV content
      const csvHeaders = [
        'Quotation Number',
        'Date',
        'Customer Name',
        'Customer Email',
        'Customer Phone',
        'Customer City',
        'Executive Name',
        'Executive Email',
        'Status',
        'Subject',
        'Items Count',
        'Subtotal',
        'Tax Amount',
        'Total Amount',
        'Valid Until',
      ];

      const csvRows = quotations.map(quotation => [
        quotation.quotationNumber,
        new Date(quotation.quotationDate).toLocaleDateString(),
        quotation.customer?.name || '',
        quotation.customer?.email || '',
        quotation.customer?.phone || '',
        quotation.customer?.city || '',
        quotation.executive?.name || '',
        quotation.executive?.email || '',
        quotation.status,
        quotation.subject || '',
        quotation.items?.length || 0,
        quotation.subtotal?.toFixed(2) || '0.00',
        quotation.taxAmount?.toFixed(2) || '0.00',
        quotation.totalAmount?.toFixed(2) || '0.00',
        quotation.validUntil ? new Date(quotation.validUntil).toLocaleDateString() : '',
      ]);

      const csvContent = [
        csvHeaders.join(','),
        ...csvRows.map(row => 
          row.map(field => 
            typeof field === 'string' && field.includes(',') 
              ? `"${field.replace(/"/g, '""')}"` 
              : field
          ).join(',')
        ),
      ].join('\n');

      // Log activity
      await ActivityLogService.log(
        request,
        'EXPORT',
        'quotations',
        null,
        `Exported ${quotations.length} quotations to CSV`
      );

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="quotations-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    }

    if (format === 'excel') {
      // For Excel format, we'll return JSON data that can be processed by a frontend library
      const excelData = quotations.map(quotation => ({
        'Quotation Number': quotation.quotationNumber,
        'Date': new Date(quotation.quotationDate).toLocaleDateString(),
        'Customer Name': quotation.customer?.name || '',
        'Customer Email': quotation.customer?.email || '',
        'Customer Phone': quotation.customer?.phone || '',
        'Customer City': quotation.customer?.city || '',
        'Customer State': quotation.customer?.state || '',
        'Executive Name': quotation.executive?.name || '',
        'Executive Email': quotation.executive?.email || '',
        'Executive Designation': quotation.executive?.designation || '',
        'Status': quotation.status,
        'Subject': quotation.subject || '',
        'Items Count': quotation.items?.length || 0,
        'Subtotal': quotation.subtotal?.toFixed(2) || '0.00',
        'Tax Amount': quotation.taxAmount?.toFixed(2) || '0.00',
        'Total Amount': quotation.totalAmount?.toFixed(2) || '0.00',
        'Valid Until': quotation.validUntil ? new Date(quotation.validUntil).toLocaleDateString() : '',
        'Notes': quotation.notes || '',
        'Terms & Conditions': quotation.termsConditions || '',
      }));

      // Log activity
      await ActivityLogService.log(
        request,
        'EXPORT',
        'quotations',
        null,
        `Exported ${quotations.length} quotations to Excel format`
      );

      return NextResponse.json({
        success: true,
        data: excelData,
        filename: `quotations-${new Date().toISOString().split('T')[0]}.xlsx`,
      });
    }

    return NextResponse.json(
      { success: false, error: 'Unsupported export format' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Export quotations error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to export quotations' },
      { status: 500 }
    );
  }
}

export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  handleExportQuotations
);
