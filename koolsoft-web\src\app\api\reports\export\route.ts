import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ReportRepository } from '@/lib/repositories/report.repository';
import {
  reportExportSchema,
  amcReportFilterSchema,
  warrantyReportFilterSchema,
  serviceReportFilterSchema,
  salesReportFilterSchema,
  customerReportFilterSchema,
} from '@/lib/validations/report.schema';
import {
  generateCSVResponse,
  prepareDataForCSV,
  generateReportFilename,
  buildErrorResponse,
} from '@/lib/utils/report-utils';
import { z } from 'zod';

/**
 * POST /api/reports/export
 * Export reports in various formats (CSV, Excel, PDF)
 * Accessible by ADMIN, MANAGER, EXECUTIVE roles
 */
async function exportReport(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate export request
    const validatedRequest = reportExportSchema.parse(body);
    
    const reportRepository = new ReportRepository();
    let reportData;
    
    // Get report data based on type
    switch (validatedRequest.reportType) {
      case 'AMC':
        const amcFilters = amcReportFilterSchema.parse(validatedRequest.filters || {});
        // Remove pagination for export (get all data)
        amcFilters.limit = 10000;
        amcFilters.page = 1;
        reportData = await reportRepository.getAMCReports(amcFilters);
        break;
        
      case 'WARRANTY':
        const warrantyFilters = warrantyReportFilterSchema.parse(validatedRequest.filters || {});
        warrantyFilters.limit = 10000;
        warrantyFilters.page = 1;
        reportData = await reportRepository.getWarrantyReports(warrantyFilters);
        break;
        
      case 'SERVICE':
        const serviceFilters = serviceReportFilterSchema.parse(validatedRequest.filters || {});
        serviceFilters.limit = 10000;
        serviceFilters.page = 1;
        reportData = await reportRepository.getServiceReports(serviceFilters);
        break;
        
      case 'SALES':
        const salesFilters = salesReportFilterSchema.parse(validatedRequest.filters || {});
        salesFilters.limit = 10000;
        salesFilters.page = 1;
        reportData = await reportRepository.getSalesReports(salesFilters);
        break;
        
      case 'CUSTOMER':
        const customerFilters = customerReportFilterSchema.parse(validatedRequest.filters || {});
        customerFilters.limit = 10000;
        customerFilters.page = 1;
        reportData = await reportRepository.getCustomerReports(customerFilters);
        break;
        
      default:
        return NextResponse.json(
          buildErrorResponse(`Unsupported report type: ${validatedRequest.reportType}`),
          { status: 400 }
        );
    }
    
    if (!reportData.data || reportData.data.length === 0) {
      return NextResponse.json(
        buildErrorResponse('No data found for the specified filters'),
        { status: 404 }
      );
    }
    
    // Generate export based on format
    switch (validatedRequest.format) {
      case 'CSV':
        return generateCSVExport(reportData.data, validatedRequest);
        
      case 'JSON':
        return generateJSONExport(reportData.data, validatedRequest);
        
      case 'EXCEL':
        // For now, return CSV format for Excel (can be enhanced later)
        return generateCSVExport(reportData.data, validatedRequest);
        
      case 'PDF':
        // PDF export can be implemented later
        return NextResponse.json(
          buildErrorResponse('PDF export not yet implemented'),
          { status: 501 }
        );
        
      default:
        return NextResponse.json(
          buildErrorResponse(`Unsupported export format: ${validatedRequest.format}`),
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('Error exporting report:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        buildErrorResponse('Validation error', error.errors),
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      buildErrorResponse('Failed to export report'),
      { status: 500 }
    );
  }
}

/**
 * Generate CSV export
 */
function generateCSVExport(data: any[], request: any) {
  const flattenedData = prepareDataForCSV(data);
  const filename = request.filename || generateReportFilename(request.reportType, 'csv');
  
  return generateCSVResponse(flattenedData, filename);
}

/**
 * Generate JSON export
 */
function generateJSONExport(data: any[], request: any) {
  const filename = request.filename || generateReportFilename(request.reportType, 'json');
  const jsonContent = JSON.stringify(data, null, 2);
  
  return new NextResponse(jsonContent, {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Content-Disposition': `attachment; filename="${filename}.json"`,
      'Cache-Control': 'no-cache',
    },
  });
}

// Export with role protection
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  exportReport
);
