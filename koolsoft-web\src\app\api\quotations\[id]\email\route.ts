import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { QuotationRepository } from '@/lib/repositories/quotation.repository';
import { ActivityLogger } from '@/lib/services/activity-logger';
import { z } from 'zod';

// Email validation schema
const emailQuotationSchema = z.object({
  to: z.string().email('Please enter a valid email address'),
  cc: z.string().optional(),
  bcc: z.string().optional(),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
  includePDF: z.boolean().default(true),
  sendCopy: z.boolean().default(false),
});

/**
 * POST /api/quotations/[id]/email
 * Send quotation via email
 */
async function handleEmailQuotation(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // Validate request body
    const validatedData = emailQuotationSchema.parse(body);

    const quotationRepository = new QuotationRepository();
    const quotation = await quotationRepository.findById(id);

      if (!quotation) {
        return NextResponse.json(
          {
            success: false,
            error: 'Quotation not found',
          },
          { status: 404 }
        );
      }

      // For now, simulate email sending (in a real implementation, you would integrate with an email service)
      console.log('Email would be sent to:', validatedData.to);
      console.log('Subject:', validatedData.subject);
      console.log('Message:', validatedData.message);

      // Log the email activity
      await ActivityLogger.log(
        request,
        'EMAIL',
        'quotations',
        id,
        `Email sent to ${validatedData.to} for quotation ${quotation.quotationNumber}`
      );

      // Update quotation status to SENT if it was DRAFT
      if (quotation.status === 'DRAFT') {
        await quotationRepository.update(id, {
          status: 'SENT',
        });
      }

      return NextResponse.json({
        success: true,
        message: 'Quotation email sent successfully',
        data: {
          sentTo: validatedData.to,
          quotationNumber: quotation.quotationNumber,
          subject: validatedData.subject,
        },
      });
  } catch (error) {
    console.error('Error sending quotation email:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email data',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to send quotation email',
      },
      { status: 500 }
    );
  }
}

export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  handleEmailQuotation
);
