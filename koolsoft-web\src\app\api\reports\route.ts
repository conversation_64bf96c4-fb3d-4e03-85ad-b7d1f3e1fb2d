import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ReportRepository } from '@/lib/repositories/report.repository';
import {
  reportTypeSchema,
  amcReportFilterSchema,
  warrantyReportFilterSchema,
  serviceReportFilterSchema,
  salesReportFilterSchema,
  customerReportFilterSchema,
} from '@/lib/validations/report.schema';
import { buildReportResponse, buildErrorResponse, validateReportFilters } from '@/lib/utils/report-utils';
import { z } from 'zod';

/**
 * GET /api/reports
 * Get reports based on type and filters
 * Accessible by ADMIN, MANAGER, EXECUTIVE roles
 */
async function getReports(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract and validate report type
    const reportType = searchParams.get('type');
    if (!reportType) {
      return NextResponse.json(
        buildErrorResponse('Report type is required'),
        { status: 400 }
      );
    }
    
    const validatedType = reportTypeSchema.parse(reportType.toUpperCase());
    
    // Extract filters from search params
    const filters: any = {};
    for (const [key, value] of searchParams.entries()) {
      if (key !== 'type' && value) {
        filters[key] = value;
      }
    }
    
    // Validate filters
    const filterValidation = validateReportFilters(filters);
    if (!filterValidation.isValid) {
      return NextResponse.json(
        buildErrorResponse('Invalid filters', filterValidation.errors),
        { status: 400 }
      );
    }
    
    const reportRepository = new ReportRepository();
    let result;
    
    // Route to appropriate report method based on type
    switch (validatedType) {
      case 'AMC':
        const amcFilters = amcReportFilterSchema.parse(filters);
        result = await reportRepository.getAMCReports(amcFilters);
        break;
        
      case 'WARRANTY':
        const warrantyFilters = warrantyReportFilterSchema.parse(filters);
        result = await reportRepository.getWarrantyReports(warrantyFilters);
        break;
        
      case 'SERVICE':
        const serviceFilters = serviceReportFilterSchema.parse(filters);
        result = await reportRepository.getServiceReports(serviceFilters);
        break;
        
      case 'SALES':
        const salesFilters = salesReportFilterSchema.parse(filters);
        result = await reportRepository.getSalesReports(salesFilters);
        break;
        
      case 'CUSTOMER':
        const customerFilters = customerReportFilterSchema.parse(filters);
        result = await reportRepository.getCustomerReports(customerFilters);
        break;
        
      default:
        return NextResponse.json(
          buildErrorResponse(`Unsupported report type: ${validatedType}`),
          { status: 400 }
        );
    }
    
    return NextResponse.json(
      buildReportResponse(result.data, result.pagination, {
        reportType: validatedType,
        filters: filters,
      })
    );
    
  } catch (error) {
    console.error('Error generating report:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        buildErrorResponse('Validation error', error.errors),
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      buildErrorResponse('Failed to generate report'),
      { status: 500 }
    );
  }
}

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  getReports
);
