import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getQuotationRepository } from '@/lib/repositories';
import { duplicateQuotationSchema } from '@/lib/validations/quotation.schema';
import { z } from 'zod';

/**
 * POST /api/quotations/[id]/duplicate
 * Duplicate a quotation
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = duplicateQuotationSchema.parse({
        ...body,
        sourceQuotationId: id,
      });

      const quotationRepository = getQuotationRepository();

      // Get the source quotation with all relations
      const sourceQuotation = await quotationRepository.findWithAllRelations(id);
      if (!sourceQuotation) {
        return NextResponse.json(
          {
            success: false,
            error: 'Source quotation not found',
          },
          { status: 404 }
        );
      }

      // Generate new quotation number
      const quotationNumber = await quotationRepository.generateQuotationNumber();

      // Create duplicate quotation with items in a transaction
      const result = await quotationRepository.prisma.$transaction(async (tx) => {
        // Create new quotation
        const newQuotation = await tx.quotations.create({
          data: {
            quotationNumber,
            customerId: validatedData.customerId || sourceQuotation.customerId,
            executiveId: sourceQuotation.executiveId,
            quotationDate: validatedData.quotationDate || new Date(),
            validUntil: validatedData.validUntil,
            status: 'DRAFT', // Always start as draft
            contactPerson: sourceQuotation.contactPerson,
            contactPhone: sourceQuotation.contactPhone,
            contactEmail: sourceQuotation.contactEmail,
            subject: sourceQuotation.subject,
            notes: validatedData.notes || `Duplicated from ${sourceQuotation.quotationNumber}`,
            termsConditions: sourceQuotation.termsConditions,
            subtotal: sourceQuotation.subtotal,
            taxAmount: sourceQuotation.taxAmount,
            totalAmount: sourceQuotation.totalAmount,
            discount: sourceQuotation.discount,
            discountType: sourceQuotation.discountType,
          },
        });

        // Duplicate quotation items
        const items = await Promise.all(
          sourceQuotation.items.map((item) =>
            tx.quotation_items.create({
              data: {
                quotationId: newQuotation.id,
                productId: item.productId,
                modelId: item.modelId,
                brandId: item.brandId,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                taxRate: item.taxRate,
                taxAmount: item.taxAmount,
                discount: item.discount,
                discountType: item.discountType,
                specifications: item.specifications,
                notes: item.notes,
                sortOrder: item.sortOrder,
              },
            })
          )
        );

        return { quotation: newQuotation, items };
      });

      // Fetch the complete new quotation with relations
      const completeQuotation = await quotationRepository.findWithAllRelations(
        result.quotation.id
      );

      return NextResponse.json({
        success: true,
        data: completeQuotation,
        message: 'Quotation duplicated successfully',
      });
    } catch (error) {
      console.error('Error duplicating quotation:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid duplicate quotation data',
            details: error.errors,
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to duplicate quotation',
        },
        { status: 500 }
      );
    }
  }
);
