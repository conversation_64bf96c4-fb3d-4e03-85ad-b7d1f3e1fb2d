import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { 
  getSalesLeadRepository, 
  getSalesOpportunityRepository, 
  getSalesProspectRepository, 
  getSalesOrderRepository 
} from '@/lib/repositories';
import { z } from 'zod';
import { startOfMonth, endOfMonth, subMonths, format } from 'date-fns';

// Dashboard filter schema
const dashboardFilterSchema = z.object({
  customerId: z.string().optional(),
  executiveId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  period: z.enum(['7d', '30d', '90d', '6m', '1y']).default('30d'),
});

type DashboardFilters = z.infer<typeof dashboardFilterSchema>;

/**
 * GET /api/sales/dashboard
 * Get comprehensive sales dashboard data including metrics, trends, and breakdowns
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const filters: DashboardFilters = {
        customerId: searchParams.get('customerId') || undefined,
        executiveId: searchParams.get('executiveId') || undefined,
        startDate: searchParams.get('startDate') || undefined,
        endDate: searchParams.get('endDate') || undefined,
        period: (searchParams.get('period') as any) || '30d',
      };

      const validatedFilters = dashboardFilterSchema.parse(filters);

      // Calculate date range based on period if not provided
      let dateRange = {
        startDate: validatedFilters.startDate,
        endDate: validatedFilters.endDate,
      };

      if (!dateRange.startDate || !dateRange.endDate) {
        const now = new Date();
        const periodDays = {
          '7d': 7,
          '30d': 30,
          '90d': 90,
          '6m': 180,
          '1y': 365,
        };

        const days = periodDays[validatedFilters.period];
        dateRange = {
          startDate: new Date(now.getTime() - days * 24 * 60 * 60 * 1000).toISOString(),
          endDate: now.toISOString(),
        };
      }

      // Get repository instances
      const salesLeadRepository = getSalesLeadRepository();
      const salesOpportunityRepository = getSalesOpportunityRepository();
      const salesProspectRepository = getSalesProspectRepository();
      const salesOrderRepository = getSalesOrderRepository();

      // Prepare filters for statistics
      const statisticsFilters = {
        customerId: validatedFilters.customerId,
        executiveId: validatedFilters.executiveId,
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      };

      // Fetch statistics from all repositories in parallel
      const [
        leadStatistics,
        opportunityStatistics,
        prospectStatistics,
        orderStatistics,
      ] = await Promise.all([
        salesLeadRepository.getStatistics(statisticsFilters),
        salesOpportunityRepository.getStatistics(statisticsFilters),
        salesProspectRepository.getStatistics(statisticsFilters),
        salesOrderRepository.getStatistics(statisticsFilters),
      ]);

      // Calculate key metrics
      const totalLeads = leadStatistics.total;
      const totalOpportunities = opportunityStatistics.total;
      const totalProspects = prospectStatistics.total;
      const totalOrders = orderStatistics.total;
      const totalRevenue = orderStatistics.totalAmount || 0;
      const averageOrderValue = orderStatistics.averageOrderValue || 0;

      // Calculate conversion rates
      const leadToOpportunityRate = totalLeads > 0 ? (totalOpportunities / totalLeads) * 100 : 0;
      const opportunityToProspectRate = totalOpportunities > 0 ? (totalProspects / totalOpportunities) * 100 : 0;
      const prospectToOrderRate = totalProspects > 0 ? (totalOrders / totalProspects) * 100 : 0;
      const overallConversionRate = totalLeads > 0 ? (totalOrders / totalLeads) * 100 : 0;

      // Prepare pipeline status breakdown
      const pipelineBreakdown = [
        { status: 'Leads', count: totalLeads, color: '#3B82F6' },
        { status: 'Opportunities', count: totalOpportunities, color: '#10B981' },
        { status: 'Prospects', count: totalProspects, color: '#8B5CF6' },
        { status: 'Orders', count: totalOrders, color: '#F59E0B' },
      ];

      // Generate monthly trends for the last 6 months
      const monthlyTrends = [];
      for (let i = 5; i >= 0; i--) {
        const monthStart = startOfMonth(subMonths(new Date(), i));
        const monthEnd = endOfMonth(monthStart);
        
        const monthFilters = {
          ...statisticsFilters,
          startDate: monthStart.toISOString(),
          endDate: monthEnd.toISOString(),
        };

        const [monthLeads, monthOpportunities, monthProspects, monthOrders] = await Promise.all([
          salesLeadRepository.getStatistics(monthFilters),
          salesOpportunityRepository.getStatistics(monthFilters),
          salesProspectRepository.getStatistics(monthFilters),
          salesOrderRepository.getStatistics(monthFilters),
        ]);

        monthlyTrends.push({
          month: format(monthStart, 'MMM yyyy'),
          leads: monthLeads.total,
          opportunities: monthOpportunities.total,
          prospects: monthProspects.total,
          orders: monthOrders.total,
          revenue: monthOrders.totalAmount || 0,
        });
      }

      // Prepare response data
      const dashboardData = {
        summary: {
          totalLeads,
          totalOpportunities,
          totalProspects,
          totalOrders,
          totalRevenue,
          averageOrderValue,
          leadToOpportunityRate: Math.round(leadToOpportunityRate * 100) / 100,
          opportunityToProspectRate: Math.round(opportunityToProspectRate * 100) / 100,
          prospectToOrderRate: Math.round(prospectToOrderRate * 100) / 100,
          overallConversionRate: Math.round(overallConversionRate * 100) / 100,
        },
        breakdown: {
          pipeline: pipelineBreakdown,
          leadsByStatus: leadStatistics.statusBreakdown || [],
          opportunitiesByStatus: opportunityStatistics.statusBreakdown || [],
          prospectsByStatus: prospectStatistics.statusBreakdown || [],
          ordersByStatus: orderStatistics.statusBreakdown || [],
        },
        trends: {
          monthly: monthlyTrends,
        },
        period: {
          startDate: dateRange.startDate,
          endDate: dateRange.endDate,
          period: validatedFilters.period,
        },
      };

      return NextResponse.json({
        success: true,
        data: dashboardData,
        filters: validatedFilters,
      });
    } catch (error) {
      console.error('Error fetching sales dashboard data:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales dashboard data',
        },
        { status: 500 }
      );
    }
  }
);
